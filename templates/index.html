<!DOCTYPE html>
<html lang="cs">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>{% block title %}OTE Dashboard{% endblock %}</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Inter', 'system-ui', 'sans-serif'],
          }
        }
      }
    }
  </script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js" defer></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
  <link rel="icon" href="{{ url_for('static', filename='images/logo.png') }}" type="image/svg+xml">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(30px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .glass-morphism {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .gradient-bg {
      background: linear-gradient(135deg, #2563eb 0%, #3b82f6 50%, #38bdf8 100%);
      position: relative;
      overflow: hidden;
    }
    
    .gradient-bg::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
      animation: float 25s ease-in-out infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px) scale(1); }
      50% { transform: translateY(-15px) scale(1.02); }
    }
    
    /* Dashboard Animation Classes */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(30px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes slideUp {
      from { opacity: 0; transform: translateY(50px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes slideRight {
      from { opacity: 0; transform: translateX(-50px); }
      to { opacity: 1; transform: translateX(0); }
    }
    
    @keyframes pulseSlow {
      0%, 100% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.8; transform: scale(1.02); }
    }
    
    .animate-fade-in {
      animation: fadeIn 1s ease-out;
    }
    
    .animate-slide-up {
      animation: slideUp 0.8s ease-out;
    }
    
    .animate-slide-right {
      animation: slideRight 1.2s ease-out;
    }
    
    .animate-pulse-slow {
      animation: pulseSlow 3s ease-in-out infinite;
    }
    
    .animate-fade-in-delay-1 {
      animation: fadeIn 0.8s ease-out 0.2s both;
    }
    
    .animate-fade-in-delay-2 {
      animation: fadeIn 0.8s ease-out 0.4s both;
    }
    
    .animate-fade-in-delay-3 {
      animation: fadeIn 0.8s ease-out 0.6s both;
    }

    /* Card Styles */
    .ote-electricity-card,
    .ote-environmental-card,
    .ote-gas-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 1rem;
      padding: 2rem;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .ote-electricity-card:hover,
    .ote-environmental-card:hover,
    .ote-gas-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .ote-agenda-item {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      padding: 1rem;
      border-radius: 0.75rem;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .ote-agenda-item:hover {
      background: rgba(255, 255, 255, 0.7);
      transform: translateX(8px);
    }

    .agenda-title {
      font-weight: 600;
      font-size: 1.1rem;
      color: #334155;
      margin-bottom: 0.25rem;
    }

    .agenda-description {
      color: #64748b;
      font-size: 0.95rem;
      line-height: 1.5;
    }

    .ote-electricity-icon {
      color: #2563eb;
    }

    .ote-environmental-icon {
      color: #059669;
    }

    .ote-gas-icon {
      color: #d97706;
    }

    /* Rotating Agenda Cards */
    .agenda-card {
      opacity: 0;
      transform: translateX(100px);
      transition: all 0.8s ease-in-out;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }

    .agenda-card.active {
      opacity: 1;
      transform: translateX(0);
      position: relative;
    }

    .agenda-indicator {
      cursor: pointer;
    }

    .agenda-indicator.active {
      transform: scale(1.3);
    }

  </style>
</head>

<body class="bg-slate-50 font-sans antialiased">
  
  <header class="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-slate-200/60">
    {% include 'partials/header.html' %}
  </header>
  
  <main class="min-h-screen">
    
    <!-- Hero Section -->
    <section class="gradient-bg h-screen px-6 flex items-start pt-20 overflow-hidden">
      <div class="max-w-screen-xl mx-auto w-full">
        <div class="grid lg:grid-cols-2 gap-8 h-full items-center">
          
          <!-- Left Column: Content + Statistics -->
          <div class="text-white space-y-6">
            <div class="space-y-6 animate-fade-in">
              <h1 class="text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
                Operátor trhu s energiemi
              </h1>
              <p class="text-xl lg:text-2xl text-indigo-100 leading-relaxed">
                OTE, a.s. je státní akciová společnost, založená 18. dubna 2001, která zajišťuje klíčové funkce pro efektivní a transparentní provoz trhu s elektřinou a plynem v České republice.
              </p>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-2 lg:grid-cols-3 gap-6 pt-8 animate-slide-up">
                              <div class="glass-morphism rounded-xl p-6 text-center transition-all duration-500 ease-in-out animate-pulse-slow">
                  <div class="w-16 h-16 bg-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <i class="fas fa-users text-white text-2xl"></i>
                  </div>
                  <div class="text-4xl font-bold text-indigo-900 mb-2">
                    <span class="counter" data-target="90">0</span>+
                  </div>
                  <div class="text-indigo-700 font-semibold text-lg">Zaměstnanců</div>
              </div>
              
              <div class="glass-morphism rounded-xl p-6 text-center transition-all duration-500 ease-in-out animate-pulse-slow">
                <div class="w-16 h-16 bg-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <i class="fas fa-building text-white text-2xl"></i>
                </div>
                <div class="text-4xl font-bold text-emerald-900 mb-2">
                  <span class="counter" data-target="34500">0</span>+
                </div>
                <div class="text-emerald-700 font-semibold text-lg">Registrovaných účastníků trhu</div>
              </div>
              
              <div class="glass-morphism rounded-xl p-6 text-center transition-all duration-500 ease-in-out animate-pulse-slow">
                <div class="w-16 h-16 bg-amber-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <i class="fas fa-industry text-white text-2xl"></i>
                </div>
                <div class="text-4xl font-bold text-amber-900 mb-2">
                  <span class="counter" data-target="36600">0</span>+
                </div>
                <div class="text-amber-700 font-semibold text-lg">Výrobních zdrojů energie</div>
              </div>
              
              <div class="glass-morphism rounded-xl p-6 text-center transition-all duration-500 ease-in-out animate-pulse-slow">
                <div class="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <i class="fas fa-bolt text-white text-2xl"></i>
                </div>
                <div class="text-4xl font-bold text-blue-900 mb-2">
                  <span class="counter" data-target="24">0</span>
                </div>
                <div class="text-blue-700 font-semibold text-lg">Let na trhu</div>
              </div>
              
              <div class="glass-morphism rounded-xl p-6 text-center transition-all duration-500 ease-in-out animate-pulse-slow">
                <div class="w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <i class="fas fa-chart-line text-white text-2xl"></i>
                </div>
                <div class="text-4xl font-bold text-purple-900 mb-2">6,4M</div>
                <div class="text-purple-700 font-semibold text-lg">OPM v elektřině</div>
              </div>
              
              <div class="glass-morphism rounded-xl p-6 text-center transition-all duration-500 ease-in-out animate-pulse-slow">
                <div class="w-16 h-16 bg-orange-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <i class="fas fa-fire text-white text-2xl"></i>
                </div>
                <div class="text-4xl font-bold text-orange-900 mb-2">2,7M+</div>
                <div class="text-orange-700 font-semibold text-lg">OPM v plynu</div>
              </div>
            </div>
          </div>

          <!-- Right Column: Combined Timeline + Agendas Card -->
          <div class="glass-morphism rounded-xl p-8 h-full animate-slide-right">
            <!-- Timeline Section -->
            <div class="mb-6">
              <h3 class="text-2xl font-bold text-slate-800 mb-4">Klíčové milníky</h3>
              
              <div class="space-y-2">
                <div class="flex items-start gap-3 p-2 rounded-lg transition-all duration-500 animate-fade-in-delay-1">
                  <div class="w-4 h-4 bg-indigo-600 rounded-full mt-1 flex-shrink-0"></div>
                  <div>
                    <h4 class="font-bold text-slate-800 text-base">2002 - Elektřina</h4>
                    <p class="text-slate-600 text-sm">Zahájení činnosti operátora trhu</p>
                  </div>
                </div>
                
                <div class="flex items-start gap-3 p-2 rounded-lg transition-all duration-500 animate-fade-in-delay-2">
                  <div class="w-4 h-4 bg-emerald-600 rounded-full mt-1 flex-shrink-0"></div>
                  <div>
                    <h4 class="font-bold text-slate-800 text-base">2004 - Emisní povolenky</h4>
                    <p class="text-slate-600 text-sm">Národní registr emisních povolenek</p>
                  </div>
                </div>
                
                <div class="flex items-start gap-3 p-2 rounded-lg transition-all duration-500 animate-fade-in-delay-3">
                  <div class="w-4 h-4 bg-amber-600 rounded-full mt-1 flex-shrink-0"></div>
                  <div>
                    <h4 class="font-bold text-slate-800 text-base">2010 - Plyn</h4>
                    <p class="text-slate-600 text-sm">Rozšíření na trh s plynem</p>
                  </div>
                </div>
                
                <div class="flex items-start gap-3 p-2 rounded-lg transition-all duration-500 animate-fade-in-delay-3">
                  <div class="w-4 h-4 bg-green-600 rounded-full mt-1 flex-shrink-0"></div>
                  <div>
                    <h4 class="font-bold text-slate-800 text-base">2013 - Záruky původu</h4>
                    <p class="text-slate-600 text-sm">Systém záruk původu elektřiny z OZE</p>
                  </div>
                </div>
                
                <div class="flex items-start gap-3 p-2 rounded-lg bg-indigo-50 border border-indigo-200 transition-all duration-500 animate-fade-in-delay-3">
                  <div class="w-4 h-4 bg-indigo-600 rounded-full mt-1 flex-shrink-0 ring-2 ring-indigo-100"></div>
                  <div>
                    <h4 class="font-bold text-indigo-900 text-base">2023 - Kapitál 1 mld</h4>
                    <p class="text-indigo-700 text-sm font-medium">Navýšení základního kapitálu</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Divider -->
            <div class="border-t border-slate-300 my-6"></div>
            
            <!-- Rotating Agendas Section -->
            <div>
              <h3 class="text-2xl font-bold text-slate-800 mb-4">Klíčové agendy</h3>
              
              <div class="relative h-80 overflow-hidden flex items-center">
                <div id="agendaRotator" class="w-full">
                  
                  <!-- Electricity Agenda -->
                  <div class="agenda-card active" data-category="electricity">
                    <div class="flex items-center gap-3 mb-4">
                      <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                        <i class="fa-solid fa-bolt text-white text-xl"></i>
                      </div>
                      <h4 class="text-xl font-bold text-blue-900">Elektřina</h4>
                    </div>
                    
                    <div class="space-y-2">
                      <div class="rounded-lg p-3 transition-all duration-500 animate-slide-up" style="background-color: #dbeafe !important; border: 1px solid #bfdbfe;">
                        <div class="flex items-center gap-2 mb-1">
                          <i class="fa-solid fa-arrows-rotate text-blue-600 text-base"></i>
                          <h5 class="font-bold text-blue-900 text-base">Organizace trhu</h5>
                        </div>
                        <p class="text-blue-700 text-sm">Denní a vnitrodenní trh s elektřinou</p>
                      </div>
                      <div class="rounded-lg p-3 transition-all duration-500 animate-slide-up" style="background-color: #dbeafe !important; border: 1px solid #bfdbfe;">
                        <div class="flex items-center gap-2 mb-1">
                          <i class="fa-solid fa-globe text-blue-600 text-base"></i>
                          <h5 class="font-bold text-blue-900 text-base">Evropská spolupráce</h5>
                        </div>
                        <p class="text-blue-700 text-sm">Výkon činnosti NEMO</p>
                      </div>
                      <div class="rounded-lg p-3 transition-all duration-500 animate-slide-up" style="background-color: #dbeafe !important; border: 1px solid #bfdbfe;">
                        <div class="flex items-center gap-2 mb-1">
                          <i class="fa-solid fa-scale-balanced text-blue-600 text-base"></i>
                          <h5 class="font-bold text-blue-900 text-base">Systémové služby</h5>
                        </div>
                        <p class="text-blue-700 text-sm">Koordinace pro stabilitu sítě</p>
                      </div>
                    </div>
                  </div>

                  <!-- Environmental Agenda -->
                  <div class="agenda-card" data-category="environmental">
                    <div class="flex items-center gap-3 mb-4">
                      <div class="w-12 h-12 bg-emerald-600 rounded-lg flex items-center justify-center shadow-lg">
                        <i class="fa-solid fa-leaf text-white text-xl"></i>
                      </div>
                      <h4 class="text-xl font-bold text-emerald-900">Environmentální nástroje</h4>
                    </div>
                    
                    <div class="space-y-2">
                      <div class="rounded-lg p-3 transition-all duration-500 animate-slide-up" style="background-color: #d1fae5 !important; border: 1px solid #a7f3d0;">
                        <div class="flex items-center gap-2 mb-1">
                          <i class="fa-solid fa-certificate text-emerald-600 text-base"></i>
                          <h5 class="font-bold text-emerald-900 text-base">Záruky původu</h5>
                        </div>
                        <p class="text-emerald-700 text-sm">Administrace systému OZE</p>
                      </div>
                      <div class="rounded-lg p-3 transition-all duration-500 animate-slide-up" style="background-color: #d1fae5 !important; border: 1px solid #a7f3d0;">
                        <div class="flex items-center gap-2 mb-1">
                          <i class="fa-solid fa-seedling text-emerald-600 text-base"></i>
                          <h5 class="font-bold text-emerald-900 text-base">Podpora OZE</h5>
                        </div>
                        <p class="text-emerald-700 text-sm">Systém podpory obnovitelných zdrojů</p>
                      </div>
                      <div class="rounded-lg p-3 transition-all duration-500 animate-slide-up" style="background-color: #d1fae5 !important; border: 1px solid #a7f3d0;">
                        <div class="flex items-center gap-2 mb-1">
                          <i class="fa-solid fa-cloud text-emerald-600 text-base"></i>
                          <h5 class="font-bold text-emerald-900 text-base">Emisní povolenky</h5>
                        </div>
                        <p class="text-emerald-700 text-sm">Národní registr emisních povolenek</p>
                      </div>
                    </div>
                  </div>

                  <!-- Gas Agenda -->
                  <div class="agenda-card" data-category="gas">
                    <div class="flex items-center gap-3 mb-4">
                      <div class="w-12 h-12 bg-amber-600 rounded-lg flex items-center justify-center shadow-lg">
                        <i class="fa-solid fa-fire text-white text-xl"></i>
                      </div>
                      <h4 class="text-xl font-bold text-amber-900">Plyn</h4>
                    </div>
                    
                    <div class="space-y-2">
                      <div class="rounded-lg p-3 transition-all duration-500 animate-slide-up" style="background-color: #fef3c7 !important; border: 1px solid #fde68a;">
                        <div class="flex items-center gap-2 mb-1">
                          <i class="fa-solid fa-arrows-rotate text-amber-600 text-base"></i>
                          <h5 class="font-bold text-amber-900 text-base">Vnitrodenní trh</h5>
                        </div>
                        <p class="text-amber-700 text-sm">Organizace obchodování s plynem</p>
                      </div>
                      <div class="rounded-lg p-3 transition-all duration-500 animate-slide-up" style="background-color: #fef3c7 !important; border: 1px solid #fde68a;">
                        <div class="flex items-center gap-2 mb-1">
                          <i class="fa-solid fa-warehouse text-amber-600 text-base"></i>
                          <h5 class="font-bold text-amber-900 text-base">Evidence zásob</h5>
                        </div>
                        <p class="text-amber-700 text-sm">Zásoby plynu v zásobnících</p>
                      </div>
                      <div class="rounded-lg p-3 transition-all duration-500 animate-slide-up" style="background-color: #fef3c7 !important; border: 1px solid #fde68a;">
                        <div class="flex items-center gap-2 mb-1">
                          <i class="fa-solid fa-chart-bar text-amber-600 text-base"></i>
                          <h5 class="font-bold text-amber-900 text-base">Bilance a statistiky</h5>
                        </div>
                        <p class="text-amber-700 text-sm">Zpracování bilancí plynu</p>
                      </div>
                    </div>
                  </div>


                </div>
                
                <!-- Progress Indicators -->
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                  <div class="agenda-indicator active w-3 h-3 rounded-full bg-blue-600 transition-all duration-300" data-target="electricity"></div>
                  <div class="agenda-indicator w-3 h-3 rounded-full bg-slate-300 transition-all duration-300" data-target="environmental"></div>
                  <div class="agenda-indicator w-3 h-3 rounded-full bg-slate-300 transition-all duration-300" data-target="gas"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <script src="{{ url_for('static', filename='js/dashboard.js') }}" defer></script>
  <script src="{{ url_for('static', filename='js/script.js') }}" defer></script>
  
  <script>
    // Counter animation
    function animateCounter(element, target) {
      let current = 0;
      const increment = target > 100 ? target / 100 : 1;
      const duration = 2000; // 2 seconds
      const steps = 60;
      const stepTime = duration / steps;
      
      const timer = setInterval(() => {
        current += increment;
        element.textContent = Math.ceil(current);
        if (current >= target) {
          element.textContent = target;
          clearInterval(timer);
        }
      }, stepTime);
    }
    
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize counters
      document.querySelectorAll('.counter').forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        animateCounter(counter, target);
      });
      
      // Agenda Card Rotation
      const agendaCards = document.querySelectorAll('.agenda-card');
      const agendaIndicators = document.querySelectorAll('.agenda-indicator');
      let currentAgendaIndex = 0;
      
      function showAgendaCard(index) {
        // Hide all cards
        agendaCards.forEach(card => card.classList.remove('active'));
        agendaIndicators.forEach(indicator => indicator.classList.remove('active'));
        
        // Show selected card
        agendaCards[index].classList.add('active');
        agendaIndicators[index].classList.add('active');
        
        // Update indicator colors
        agendaIndicators.forEach((indicator, i) => {
          if (i === index) {
            if (index === 0) indicator.className = 'agenda-indicator active w-3 h-3 rounded-full bg-blue-600 transition-all duration-300';
            if (index === 1) indicator.className = 'agenda-indicator active w-3 h-3 rounded-full bg-emerald-600 transition-all duration-300';
            if (index === 2) indicator.className = 'agenda-indicator active w-3 h-3 rounded-full bg-amber-600 transition-all duration-300';
          } else {
            indicator.className = 'agenda-indicator w-3 h-3 rounded-full bg-slate-300 transition-all duration-300';
          }
        });
      }
      
      function nextAgendaCard() {
        currentAgendaIndex = (currentAgendaIndex + 1) % agendaCards.length;
        showAgendaCard(currentAgendaIndex);
      }
      
      // Auto-rotate every 6 seconds for dashboard
      setInterval(nextAgendaCard, 6000);
      
      // Manual navigation
      agendaIndicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
          currentAgendaIndex = index;
          showAgendaCard(index);
        });
      });
    });
  </script>
</body>

</html>




