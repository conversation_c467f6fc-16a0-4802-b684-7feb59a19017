<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON> vni<PERSON><PERSON><PERSON><PERSON><PERSON> (IDA2)</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js" defer></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
            animation: {
              'gradient-x': 'gradient-x 15s ease infinite',
            },
            keyframes: {
              'gradient-x': {
                '0%, 100%': {
                  'background-size': '200% 200%',
                  'background-position': 'left center'
                },
                '50%': {
                  'background-size': '200% 200%',
                  'background-position': 'right center'
                }
              },
            }
          }
        }
      }
    </script>
</head>
<body class="bg-gray-50 text-gray-800">
    {% include 'partials/header.html' %}

    <section class="market-result bg-white py-6 text-center shadow-sm border-b border-gray-200">
        <h1 id="market-result-title" class="text-2xl font-semibold text-blue-800">
            Výsledky vnitrodenních aukcí ČR (IDA2) - <span id="market-date" class="font-bold">Načítání...</span>
        </h1>
    </section>

    <main class="dashboard-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="col-span-1 md:col-span-3 flex gap-6 rounded-2xl border border-blue-200 p-2" style="background-color: #eaf3fb;">
                <div class="flex-1 rounded-xl shadow-lg overflow-hidden p-6 text-center bg-white">
                    <div class="text-sm font-semibold uppercase tracking-wide text-gray-600 mb-4">BASE LOAD</div>
                    <div class="text-3xl font-bold text-blue-800 mb-1" id="base-load">---</div>
                    <div class="text-xs text-gray-500">EUR/MWh</div>
                </div>
                <div class="flex-1 rounded-xl shadow-lg overflow-hidden p-6 text-center bg-white">
                    <div class="text-sm font-semibold uppercase tracking-wide text-gray-600 mb-4">PEAK LOAD</div>
                    <div class="text-3xl font-bold text-blue-800 mb-1" id="peak-load">---</div>
                    <div class="text-xs text-gray-500">EUR/MWh</div>
                </div>
                <div class="flex-1 rounded-xl shadow-lg overflow-hidden p-6 text-center bg-white">
                    <div class="text-sm font-semibold uppercase tracking-wide text-gray-600 mb-4">OFFPEAK LOAD</div>
                    <div class="text-3xl font-bold text-blue-800 mb-1" id="offpeak-load">---</div>
                    <div class="text-xs text-gray-500">EUR/MWh</div>
                </div>
            </div>
            <div class="rounded-xl shadow-lg overflow-hidden p-6 text-center bg-white">
                <div class="text-sm font-semibold uppercase tracking-wide text-gray-600 mb-4">CELKOVÉ MNOŽSTVÍ</div>
                <div class="text-3xl font-bold text-blue-800 mb-1" id="total-load">---</div>
                <div class="text-xs text-gray-500">MWh</div>
            </div>
        </div>
        <div class="dashboard bg-white rounded-2xl shadow-xl overflow-hidden p-4 sm:p-6">
            <div class="chart-container relative w-full h-[500px] sm:h-[600px]">
                <canvas id="oteChart"></canvas>
            </div>
        </div>
    </main>

    <script>
      function updateDateTime() {
        const now = new Date();
        const options = {
          day: '2-digit', month: '2-digit', year: 'numeric',
          hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false
        };
        const dateTimeElement = document.getElementById('dateTime');
        if (dateTimeElement) {
            dateTimeElement.textContent = now.toLocaleString('cs-CZ', options);
        }
      }
      let timeUpdateInterval;
      document.addEventListener('DOMContentLoaded', () => {
        updateDateTime();
        timeUpdateInterval = setInterval(updateDateTime, 1000);
        document.addEventListener('visibilitychange', () => {
          if (document.hidden) { clearInterval(timeUpdateInterval); }
          else { updateDateTime(); timeUpdateInterval = setInterval(updateDateTime, 1000); }
        });
      });
    </script>
    <script>
      function getTodayDate() {
        const now = new Date();
        const yyyy = now.getFullYear();
        const mm = String(now.getMonth() + 1).padStart(2, '0');
        const dd = String(now.getDate()).padStart(2, '0');
        return `${yyyy}-${mm}-${dd}`;
      }

      // Function to get cached data from localStorage
      function getCachedData(key) {
        const cached = localStorage.getItem(key);
        if (cached) {
          return JSON.parse(cached);
        }
        return null;
      }

      // Function to set data in localStorage
      function setCachedData(key, data) {
        localStorage.setItem(key, JSON.stringify(data));
      }

      async function fetchIdaSummary() {
        const date = getTodayDate();
        const cacheKey = `ida-summary-${date}`;
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
          return cachedData;
        }

        const url = `/api/ida-data?ida_session=IDA2&report_date=${date}`;
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error('Chyba při načítání dat z backendu');
          const data = await response.json();
          setCachedData(cacheKey, data);
          return data;
        } catch (e) {
          return null;
        }
      }

      async function updateSummaryCards() {
        const summary = await fetchIdaSummary();
        if (summary && summary.data) {
          for (const item of summary.data) {
            if (item.index === 'BASE LOAD') {
              document.getElementById('base-load').textContent = item.price || '---';
            } else if (item.index === 'PEAK LOAD') {
              document.getElementById('peak-load').textContent = item.price || '---';
            } else if (item.index === 'OFFPEAK LOAD') {
              document.getElementById('offpeak-load').textContent = item.price || '---';
            }
          }
          document.getElementById('total-load').textContent = summary.total_amount !== null ? summary.total_amount.toFixed(1) : '---';
        } else {
          document.getElementById('base-load').textContent = '---';
          document.getElementById('peak-load').textContent = '---';
          document.getElementById('offpeak-load').textContent = '---';
          document.getElementById('total-load').textContent = '---';
        }
      }

      async function fetchData() {
        const date = getTodayDate();
        const url = `https://www.ote-cr.cz/cs/kratkodobe-trhy/elektrina/vnitrodenni-aukce-ida/@@chart-data?report_date=${date}&ida_session=IDA2&time_resolution=PT15M`;
        try {
          const response = await fetch(url);
          if (!response.ok) {
            throw new Error('Chyba při načítání dat z URL: ' + response.statusText);
          }
          const data = await response.json();
          return data;
        } catch (error) {
          console.error("Chyba při načítání dat z URL:", error);
          return null;
        }
      }

      function formatHourLabels(dataPoints) {
        const labels = [];
        dataPoints.forEach((point, index) => {
          if (point % 4 === 0) {
            const hour = point / 4;
            labels.push(hour <= 24 ? hour.toString() : '');
          } else {
            labels.push('');
          }
        });
        return labels;
      }

      function createChart(canvasId, chartData) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        if (window.myChart) {
          window.myChart.destroy();
        }
        window.myChart = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: chartData.labels,
            datasets: [
              {
                label: 'Množství (MWh)',
                data: chartData.mnozstvi,
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 0.8)',
                borderWidth: 2,
                type: 'bar',
                yAxisID: 'y-axis-2',
                order: 1,
              },
              {
                label: 'Cena (EUR/MWh)',
                data: chartData.prumerCena,
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                borderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 7,
                pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                pointBorderColor: 'rgba(255, 99, 132, 1)',
                type: 'line',
                yAxisID: 'y-axis-1',
                order: 2,
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              x: {
                title: {
                  display: true,
                  text: 'Hodina',
                  color: '#333'
                },
                grid: {
                  display: true,
                  color: '#e0e0e0'
                },
                ticks: {
                  color: '#333',
                  font: {
                    size: 12,
                    family: 'Roboto',
                    weight: 'bold'
                  },
                  padding: 10,
                  maxRotation: 0,
                  minRotation: 0,
                  autoSkip: false,
                  callback: function(value, index) {
                    return chartData.labels[index];
                  }
                }
              },
              'y-axis-1': {
                type: 'linear',
                position: 'left',
                title: {
                  display: true,
                  text: 'Cena (EUR/MWh)',
                  color: '#333'
                },
                grid: {
                  display: true,
                  color: '#e0e0e0'
                },
                ticks: {
                  color: '#333',
                  autoSkip: true,
                },
              },
              'y-axis-2': {
                type: 'linear',
                position: 'right',
                title: {
                  display: true,
                  text: 'Množství (MWh)',
                  color: '#333'
                },
                grid: {
                  display: false
                },
                ticks: {
                  color: '#333',
                  autoSkip: true,
                },
              }
            },
            plugins: {
              legend: {
                display: true,
                position: 'top',
                labels: {
                  color: '#333',
                  boxWidth: 30,
                  boxHeight: 30,
                  padding: 20,
                }
              },
              tooltip: {
                mode: 'index',
                intersect: false,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: '#ddd',
                borderWidth: 1,
                callbacks: {
                  label: function(context) {
                    let label = context.dataset.label || '';
                    if (label) {
                      label += ': ';
                    }
                    if (context.parsed.y !== null) {
                      label += context.parsed.y.toFixed(2);
                    }
                    return label;
                  }
                }
              }
            },
            elements: {
              line: {
                tension: 0.4
              }
            }
          }
        });
      }

      async function updateDashboard() {
        const data = await fetchData();
        if (data) {
          const dataLines = data.data.dataLine;
          const graphTitle = data.graph.title;
          const dateMatch = graphTitle.match(/\d{2}\.\d{2}\.\d{4}/);
          if (dateMatch) {
            document.getElementById('market-date').textContent = dateMatch[0];
          } else {
            document.getElementById('market-date').textContent = 'N/A';
          }
          if (dataLines && dataLines.length > 0) {
            const quantityData = dataLines[0].point.map(p => ({ x: parseInt(p.x, 10), y: p.y }));
            const priceData = dataLines[1].point.map(p => ({ x: parseInt(p.x, 10), y: p.y }));
            const labels = formatHourLabels(quantityData.map(p => p.x));
            const chartData = {
              labels: labels,
              mnozstvi: quantityData.map(p => p.y),
              prumerCena: priceData.map(p => p.y)
            };
            createChart('oteChart', chartData);
          }
        } else {
          document.getElementById('market-date').textContent = 'Chyba při načítání dat';
        }
      }

      window.onload = function() {
        updateDashboard();
        updateSummaryCards();
        updateDateTime();
        setInterval(updateDashboard, 5 * 60 * 1000);
        setInterval(updateSummaryCards, 5 * 60 * 1000);
      };
    </script>
    <script src="{{ url_for('static', filename='js/IDA2.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}" defer></script>
</body>
</html>
