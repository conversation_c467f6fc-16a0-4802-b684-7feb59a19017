<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON> vni<PERSON><PERSON><PERSON><PERSON><PERSON> (IDA1)</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js" defer></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
            animation: {
              'gradient-x': 'gradient-x 15s ease infinite',
            },
            keyframes: {
              'gradient-x': {
                '0%, 100%': {
                  'background-size': '200% 200%',
                  'background-position': 'left center'
                },
                '50%': {
                  'background-size': '200% 200%',
                  'background-position': 'right center'
                }
              },
            }
          }
        }
      }
    </script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
</head>
<body class="bg-gray-50 text-gray-800">
    {% include 'partials/header.html' %}

    <section class="market-result bg-white py-6 text-center shadow-sm border-b border-gray-200">
        <h1 id="market-result-title" class="text-2xl font-semibold text-blue-800">
            Výsledky vnitrodenních aukcí ČR (IDA1) - <span id="market-date" class="font-bold">Načítání...</span>
        </h1>
    </section>

    <main class="dashboard-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="col-span-1 md:col-span-3 flex gap-6 rounded-2xl border border-blue-200 p-2" style="background-color: #eaf3fb;">
                <div class="flex-1 rounded-xl shadow-lg overflow-hidden p-6 text-center bg-white">
                    <div class="text-sm font-semibold uppercase tracking-wide text-gray-600 mb-4">BASE LOAD</div>
                    <div class="text-3xl font-bold text-blue-800 mb-1" id="base-load">---</div>
                    <div class="text-xs text-gray-500">EUR/MWh</div>
                </div>
                <div class="flex-1 rounded-xl shadow-lg overflow-hidden p-6 text-center bg-white">
                    <div class="text-sm font-semibold uppercase tracking-wide text-gray-600 mb-4">PEAK LOAD</div>
                    <div class="text-3xl font-bold text-blue-800 mb-1" id="peak-load">---</div>
                    <div class="text-xs text-gray-500">EUR/MWh</div>
                </div>
                <div class="flex-1 rounded-xl shadow-lg overflow-hidden p-6 text-center bg-white">
                    <div class="text-sm font-semibold uppercase tracking-wide text-gray-600 mb-4">OFFPEAK LOAD</div>
                    <div class="text-3xl font-bold text-blue-800 mb-1" id="offpeak-load">---</div>
                    <div class="text-xs text-gray-500">EUR/MWh</div>
                </div>
            </div>
            <div class="rounded-xl shadow-lg overflow-hidden p-6 text-center bg-white">
                <div class="text-sm font-semibold uppercase tracking-wide text-gray-600 mb-4">CELKOVÉ MNOŽSTVÍ</div>
                <div class="text-3xl font-bold text-blue-800 mb-1" id="total-load">---</div>
                <div class="text-xs text-gray-500">MWh</div>
            </div>
        </div>
        <div class="dashboard bg-white rounded-2xl shadow-xl overflow-hidden p-4 sm:p-6">
            <div class="chart-container relative w-full h-[500px] sm:h-[600px]">
                <canvas id="oteChart"></canvas>
            </div>
        </div>
    </main>

    <script>
      function updateDateTime() {
        const now = new Date();
        const options = {
          day: '2-digit', month: '2-digit', year: 'numeric',
          hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false
        };
        const dateTimeElement = document.getElementById('dateTime');
        if (dateTimeElement) {
            dateTimeElement.textContent = now.toLocaleString('cs-CZ', options);
        }
      }
      let timeUpdateInterval;
      document.addEventListener('DOMContentLoaded', () => {
        updateDateTime();
        timeUpdateInterval = setInterval(updateDateTime, 1000);
        document.addEventListener('visibilitychange', () => {
          if (document.hidden) { clearInterval(timeUpdateInterval); }
          else { updateDateTime(); timeUpdateInterval = setInterval(updateDateTime, 1000); }
        });
      });
    </script>
    <script src="{{ url_for('static', filename='js/IDA1.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}" defer></script>
    <script>
      // Function to get cached data from localStorage
      function getCachedData(key) {
        const cached = localStorage.getItem(key);
        if (cached) {
          return JSON.parse(cached);
        }
        return null;
      }

      // Function to set data in localStorage
      function setCachedData(key, data) {
        localStorage.setItem(key, JSON.stringify(data));
      }

      async function fetchIdaSummary() {
        const date = getTodayDate();
        const cacheKey = `ida-summary-${date}`;
        const cachedData = getCachedData(cacheKey);
        if (cachedData) {
          return cachedData;
        }

        const url = `/api/ida-data?ida_session=IDA1&report_date=${date}`;
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error('Chyba při načítání dat z backendu');
          const data = await response.json();
          setCachedData(cacheKey, data);
          return data;
        } catch (e) {
          return null;
        }
      }

      async function updateSummaryCards() {
        const summary = await fetchIdaSummary();
        if (summary && summary.data) {
          for (const item of summary.data) {
            if (item.index === 'BASE LOAD') {
              document.getElementById('base-load').textContent = item.price || '---';
            } else if (item.index === 'PEAK LOAD') {
              document.getElementById('peak-load').textContent = item.price || '---';
            } else if (item.index === 'OFFPEAK LOAD') {
              document.getElementById('offpeak-load').textContent = item.price || '---';
            }
          }
          document.getElementById('total-load').textContent = summary.total_amount !== null ? summary.total_amount.toFixed(1) : '---';
        } else {
          document.getElementById('base-load').textContent = '---';
          document.getElementById('peak-load').textContent = '---';
          document.getElementById('offpeak-load').textContent = '---';
          document.getElementById('total-load').textContent = '---';
        }
      }

      window.onload = function() {
        updateDashboard();
        updateSummaryCards();
        updateDateTime();
        setInterval(updateDashboard, 5 * 60 * 1000);
        setInterval(updateSummaryCards, 5 * 60 * 1000);
      };
    </script>
</body>
</html>
