<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Optimalizovan<PERSON> na<PERSON> dat - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="{{ url_for('static', filename='js/optimized-data-loader.js') }}"></script>
    <style>
        .loading { opacity: 0.5; }
        .error { color: red; }
        .success { color: green; }
        .performance-metric {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Optimalizovan<PERSON> na<PERSON>ání OTE dat</h1>
        
        <!-- Performance metriky -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="performance-metric">
                <h3 class="font-semibold">Rychlost načítání</h3>
                <p id="loadTime" class="text-2xl font-bold">-- ms</p>
            </div>
            <div class="performance-metric">
                <h3 class="font-semibold">Cache hit rate</h3>
                <p id="cacheHitRate" class="text-2xl font-bold">--%</p>
            </div>
            <div class="performance-metric">
                <h3 class="font-semibold">Paralelní requesty</h3>
                <p id="parallelRequests" class="text-2xl font-bold">--</p>
            </div>
        </div>
        
        <!-- Ovládací tlačítka -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 class="text-xl font-semibold mb-4">Testování optimalizací</h2>
            <div class="flex flex-wrap gap-4">
                <button id="loadAllBtn" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Načíst všechna data (paralelně)
                </button>
                <button id="loadElectricityBtn" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Načíst elektřina data
                </button>
                <button id="loadGasBtn" class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    Načíst plyn data
                </button>
                <button id="loadIdaBtn" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Načíst IDA1 data
                </button>
                <button id="loadAllIdaBtn" class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                    Načíst všechna IDA data (paralelně)
                </button>
                <button id="loadIda2Btn" class="bg-pink-500 text-white px-4 py-2 rounded hover:bg-pink-600">
                    Načíst IDA2 data
                </button>
                <button id="loadIda3Btn" class="bg-teal-500 text-white px-4 py-2 rounded hover:bg-teal-600">
                    Načíst IDA3 data
                </button>
                <button id="clearCacheBtn" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    Vymazat cache
                </button>
            </div>
        </div>
        
        <!-- Status a logy -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 class="text-xl font-semibold mb-4">Status</h2>
            <div id="status" class="mb-4 p-3 bg-gray-100 rounded">
                Připraven k testování...
            </div>
            <div id="logs" class="bg-black text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto">
                <div>Optimalizovaný data loader inicializován...</div>
            </div>
        </div>
        
        <!-- Výsledky -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Elektřina data -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h3 class="text-lg font-semibold mb-4">Elektřina data</h3>
                <div id="electricityData" class="text-sm">
                    <p class="text-gray-500">Žádná data načtena</p>
                </div>
            </div>
            
            <!-- Plyn data -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h3 class="text-lg font-semibold mb-4">Plyn data</h3>
                <div id="gasData" class="text-sm">
                    <p class="text-gray-500">Žádná data načtena</p>
                </div>
            </div>
            
            <!-- IDA data -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h3 class="text-lg font-semibold mb-4">IDA1 data</h3>
                <div id="idaData" class="text-sm">
                    <p class="text-gray-500">Žádná data načtena</p>
                </div>
            </div>
            
            <!-- Performance graf -->
            <div class="bg-white p-6 rounded-lg shadow-md">
                <h3 class="text-lg font-semibold mb-4">Performance historie</h3>
                <canvas id="performanceChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <script>
        // Performance tracking
        let performanceData = {
            loadTimes: [],
            cacheHits: 0,
            totalRequests: 0,
            parallelRequests: 0
        };

        // DOM elementy
        const statusEl = document.getElementById('status');
        const logsEl = document.getElementById('logs');
        const loadTimeEl = document.getElementById('loadTime');
        const cacheHitRateEl = document.getElementById('cacheHitRate');
        const parallelRequestsEl = document.getElementById('parallelRequests');

        // Utility funkce
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logsEl.appendChild(logEntry);
            logsEl.scrollTop = logsEl.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            statusEl.textContent = message;
            statusEl.className = `mb-4 p-3 rounded ${type === 'error' ? 'bg-red-100 text-red-800' : 
                                                   type === 'success' ? 'bg-green-100 text-green-800' : 
                                                   'bg-gray-100'}`;
        }

        function updatePerformanceMetrics() {
            const avgLoadTime = performanceData.loadTimes.length > 0 ? 
                Math.round(performanceData.loadTimes.reduce((a, b) => a + b, 0) / performanceData.loadTimes.length) : 0;
            
            const cacheHitRate = performanceData.totalRequests > 0 ? 
                Math.round((performanceData.cacheHits / performanceData.totalRequests) * 100) : 0;

            loadTimeEl.textContent = `${avgLoadTime} ms`;
            cacheHitRateEl.textContent = `${cacheHitRate}%`;
            parallelRequestsEl.textContent = performanceData.parallelRequests;
        }

        function displayData(elementId, data, title) {
            const element = document.getElementById(elementId);
            if (data && data.data && Array.isArray(data.data)) {
                element.innerHTML = `
                    <p class="font-semibold text-green-600">✓ Načteno ${data.data.length} položek</p>
                    <p class="text-xs text-gray-500">Poslední aktualizace: ${data.last_update || 'N/A'}</p>
                    <div class="mt-2 max-h-32 overflow-y-auto">
                        ${data.data.slice(0, 5).map(item =>
                            `<div class="text-xs border-b py-1">
                                <strong>${item.commodity || item.tab || 'N/A'}</strong>
                                ${item.price ? ` - ${item.price} ${item.currency || ''}` : ''}
                                ${item.amount ? ` - ${item.amount} ${item.currency || ''}` : ''}
                            </div>`
                        ).join('')}
                        ${data.data.length > 5 ? `<div class="text-xs text-gray-500 mt-1">... a ${data.data.length - 5} dalších</div>` : ''}
                    </div>
                `;
            } else {
                element.innerHTML = `<p class="text-red-500">Chyba při načítání dat</p>`;
            }
        }

        function displayIdaData(elementId, data, title) {
            const element = document.getElementById(elementId);
            if (data && data.data && Array.isArray(data.data)) {
                const performanceInfo = data.performance ?
                    `<p class="text-xs text-blue-500">⚡ Načteno za ${data.performance.loadTime || data.performance.load_time_ms || 'N/A'}ms</p>` : '';

                element.innerHTML = `
                    <p class="font-semibold text-green-600">✓ ${title} - ${data.data.length} položek</p>
                    ${performanceInfo}
                    <p class="text-xs text-gray-500">Datum: ${data.used_date || 'N/A'}</p>
                    <div class="mt-2 max-h-32 overflow-y-auto">
                        ${data.data.map(item =>
                            `<div class="text-xs border-b py-1">
                                <strong>${item.index}</strong>: ${item.price} EUR/MWh
                                ${item.amount ? ` (${item.amount} MWh)` : ''}
                            </div>`
                        ).join('')}
                        ${data.total_amount ? `<div class="text-xs text-gray-600 mt-1 font-semibold">Celkem: ${data.total_amount} MWh</div>` : ''}
                    </div>
                `;
            } else if (data && data.error) {
                element.innerHTML = `<p class="text-red-500">Chyba: ${data.error}</p>`;
            } else {
                element.innerHTML = `<p class="text-red-500">Chyba při načítání ${title} dat</p>`;
            }
        }

        // Event listenery pro tlačítka
        document.getElementById('loadAllBtn').addEventListener('click', async () => {
            const startTime = performance.now();
            updateStatus('Načítám všechna data paralelně...', 'info');
            log('Spouštím paralelní načítání všech dat');
            
            try {
                const data = await window.optimizedDataLoader.loadAllData();
                const loadTime = Math.round(performance.now() - startTime);
                
                performanceData.loadTimes.push(loadTime);
                performanceData.totalRequests++;
                performanceData.parallelRequests = 3; // electricity, gas, ida1
                
                updateStatus(`Všechna data načtena za ${loadTime}ms`, 'success');
                log(`Paralelní načítání dokončeno za ${loadTime}ms`);
                
                // Zobraz data
                if (data.electricity) displayData('electricityData', data.electricity, 'Elektřina');
                if (data.gas) displayData('gasData', data.gas, 'Plyn');
                if (data.ida1) displayData('idaData', data.ida1, 'IDA1');
                
                updatePerformanceMetrics();
            } catch (error) {
                updateStatus(`Chyba: ${error.message}`, 'error');
                log(`Chyba při načítání: ${error.message}`);
            }
        });

        document.getElementById('loadElectricityBtn').addEventListener('click', async () => {
            const startTime = performance.now();
            updateStatus('Načítám elektřina data...', 'info');
            
            try {
                const data = await window.optimizedDataLoader.fetchWithTimeout('/api/electricity-data');
                const loadTime = Math.round(performance.now() - startTime);
                
                performanceData.loadTimes.push(loadTime);
                performanceData.totalRequests++;
                
                updateStatus(`Elektřina data načtena za ${loadTime}ms`, 'success');
                displayData('electricityData', data, 'Elektřina');
                updatePerformanceMetrics();
            } catch (error) {
                updateStatus(`Chyba: ${error.message}`, 'error');
            }
        });

        document.getElementById('loadAllIdaBtn').addEventListener('click', async () => {
            const startTime = performance.now();
            updateStatus('Načítám všechna IDA data paralelně...', 'info');
            log('Spouštím paralelní načítání všech IDA dat');

            try {
                const data = await window.optimizedDataLoader.loadAllIdaData();
                const loadTime = Math.round(performance.now() - startTime);

                performanceData.loadTimes.push(loadTime);
                performanceData.totalRequests++;
                performanceData.parallelRequests = 3; // IDA1, IDA2, IDA3

                updateStatus(`Všechna IDA data načtena za ${loadTime}ms`, 'success');
                log(`Paralelní načítání IDA dokončeno za ${loadTime}ms`);

                // Zobraz výsledky
                if (data.ida1) displayIdaData('idaData', data.ida1, 'IDA1');
                if (data.ida2) log(`IDA2 načteno: ${data.ida2.performance?.loadTime || 'N/A'}ms`);
                if (data.ida3) log(`IDA3 načteno: ${data.ida3.performance?.loadTime || 'N/A'}ms`);

                updatePerformanceMetrics();
            } catch (error) {
                updateStatus(`Chyba: ${error.message}`, 'error');
                log(`Chyba při načítání IDA dat: ${error.message}`);
            }
        });

        document.getElementById('loadIda2Btn').addEventListener('click', async () => {
            const startTime = performance.now();
            updateStatus('Načítám IDA2 data...', 'info');

            try {
                const data = await window.optimizedDataLoader.loadIdaCardDataOnly('IDA2');
                const loadTime = Math.round(performance.now() - startTime);

                performanceData.loadTimes.push(loadTime);
                performanceData.totalRequests++;

                updateStatus(`IDA2 data načtena za ${loadTime}ms`, 'success');
                log(`IDA2 načteno za ${loadTime}ms`);
                displayIdaData('idaData', data, 'IDA2');
                updatePerformanceMetrics();
            } catch (error) {
                updateStatus(`Chyba: ${error.message}`, 'error');
                log(`Chyba při načítání IDA2: ${error.message}`);
            }
        });

        document.getElementById('loadIda3Btn').addEventListener('click', async () => {
            const startTime = performance.now();
            updateStatus('Načítám IDA3 data...', 'info');

            try {
                const data = await window.optimizedDataLoader.loadIdaCardDataOnly('IDA3');
                const loadTime = Math.round(performance.now() - startTime);

                performanceData.loadTimes.push(loadTime);
                performanceData.totalRequests++;

                updateStatus(`IDA3 data načtena za ${loadTime}ms`, 'success');
                log(`IDA3 načteno za ${loadTime}ms`);
                displayIdaData('idaData', data, 'IDA3');
                updatePerformanceMetrics();
            } catch (error) {
                updateStatus(`Chyba: ${error.message}`, 'error');
                log(`Chyba při načítání IDA3: ${error.message}`);
            }
        });

        document.getElementById('clearCacheBtn').addEventListener('click', () => {
            window.optimizedDataLoader.cleanup();
            window.optimizedDataLoader = new OptimizedDataLoader();
            updateStatus('Cache vymazána', 'success');
            log('Cache byla vymazána a data loader reinicializován');

            // Reset performance dat
            performanceData = {
                loadTimes: [],
                cacheHits: 0,
                totalRequests: 0,
                parallelRequests: 0
            };
            updatePerformanceMetrics();
        });

        // Inicializace
        log('Demo stránka připravena k testování');
        updatePerformanceMetrics();

        // Listen na data refresh eventy
        window.addEventListener('dataRefreshed', (event) => {
            log('Background refresh dokončen');
            performanceData.cacheHits++;
            updatePerformanceMetrics();
        });
    </script>
</body>
</html>
