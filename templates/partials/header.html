<header class="bg-gradient-to-r from-blue-800 via-blue-700 to-blue-900 animate-gradient-x text-white shadow-lg sticky top-0 z-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center h-20">
    <div class="flex items-center space-x-4">
      <img src="{{ url_for('static', filename='OTE.png') }}" alt="Logo OTE" class="h-16 hover:scale-105 transition-transform duration-300">
      <div class="text-xl font-semibold hidden sm:block">Spojujeme trhy a příležitosti</div>
    </div>
    <div class="text-xl font-medium glass-effect px-6 py-3 rounded-full" id="dateTime">{{ current_time }}</div>
  </div>
</header>
<div id="autoSwitchProgressBarWrapper" class="w-full h-2 bg-gray-200 fixed top-[80px] left-0 z-50">
  <div id="autoSwitchProgressBar" class="h-2 bg-gradient-to-r from-blue-500 to-amber-500 transition-all duration-1000" style="width: 100%"></div>
</div> 
