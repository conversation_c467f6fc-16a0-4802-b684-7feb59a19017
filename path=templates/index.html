<body class="bg-gray-50">
  {% include 'partials/header.html' %}

  <main class="tv-container space-y-16">
    <!-- Hero Section -->
    <section class="tv-card p-12 bg-gradient-to-br from-ote-blue to-[#003366] text-white">
      <div class="max-w-4xl mx-auto text-center">
        <h1 class="text-5xl font-black mb-6 animate-slide-up">
          <span class="bg-clip-text text-transparent bg-gradient-to-r from-blue-200 to-white">
            OTE, a.s.
          </span>
        </h1>
        <p class="text-xl text-blue-100 leading-relaxed mb-8">
          Řídíme energetickou budoucnost České republiky prostřednictvím inovativních řešení
          a transparentního obchodování s elektřinou a plynem.
        </p>
        
        <!-- Stats Grid -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          <div class="stat-card">
            <div class="text-4xl font-bold">22+</div>
            <div class="text-sm opacity-90">Let na trhu</div>
          </div>
          <div class="stat-card">
            <div class="text-4xl font-bold">1B+</div>
            <div class="text-sm opacity-90">Kč základní kapitál</div>
          </div>
          <div class="stat-card">
            <div class="text-4xl font-bold">100%</div>
            <div class="text-sm opacity-90">Státní vlastnictví</div>
          </div>
          <div class="stat-card">
            <div class="text-4xl font-bold">24/7</div>
            <div class="text-sm opacity-90">Operační dostupnost</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Milestone Timeline -->
    <section class="tv-card p-12 bg-white">
      <h2 class="text-3xl font-black text-ote-blue mb-8">Naše historie</h2>
      <div class="relative timeline-container">
        <!-- Vertical Timeline Item -->
        <div class="timeline-item">
          <div class="timeline-marker bg-ote-blue">
            <i class="fas fa-bolt text-white"></i>
          </div>
          <div class="timeline-content">
            <h3 class="text-xl font-bold text-gray-900">2002 - Start elektrotrhu</h3>
            <p class="text-gray-600">Začátek organizace obchodování s elektřinou</p>
          </div>
        </div>
        
        <!-- Additional timeline items with different icons -->
        <!-- ... existing timeline items reworked into vertical layout ... -->
      </div>
    </section>

    <!-- Market Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Electricity -->
      <section class="market-section bg-gradient-to-br from-ote-blue/5 to-ote-blue/20">
        <div class="section-header">
          <i class="fas fa-solar-panel text-4xl text-ote-blue"></i>
          <h2>Elektroenergetika</h2>
          <div class="section-gradient-line bg-ote-blue"></div>
        </div>
        
        <!-- Agenda Items as Interactive Cards -->
        <div class="agenda-grid">
          <div class="agenda-card hover:border-ote-blue">
            <div class="agenda-icon bg-ote-blue">
              <i class="fas fa-chart-network"></i>
            </div>
            <div>
              <h3>Obchodní platformy</h3>
              <p>Denmí a vnitrodenní trh s elektřinou</p>
            </div>
          </div>
          <!-- More agenda cards ... -->
        </div>
      </section>

      <!-- Gas -->
      <section class="market-section bg-gradient-to-br from-[#FFBF3F]/5 to-[#FFBF3F]/20">
        <!-- Similar structure as electricity section with orange accents -->
      </section>
    </div>
  </main>

  <style>
    /* New Interactive Elements */
    .stat-card {
      @apply p-6 rounded-xl backdrop-blur-sm bg-white/10 transition-all hover:bg-white/20;
    }

    .timeline-item {
      @apply relative pl-16 pb-8 border-l-2 border-ote-blue/20;
    }

    .timeline-marker {
      @apply absolute left-[-17px] top-0 w-8 h-8 rounded-full flex items-center justify-center shadow-lg;
    }

    .market-section {
      @apply p-8 rounded-2xl transition-all hover:shadow-xl;
    }

    .agenda-card {
      @apply p-6 rounded-xl bg-white flex items-start gap-4 border-2 border-transparent transition-all;
    }

    .agenda-icon {
      @apply w-12 h-12 rounded-lg flex items-center justify-center text-white;
    }

    .section-header {
      @apply mb-8 relative pb-6;
    }

    .section-gradient-line {
      @apply absolute bottom-0 left-0 w-24 h-1 rounded-full opacity-50;
    }
  </style>
</body> 