# OTE Dashboard

Webová aplikace pro zobrazování dat z Operátora trhu s elektřinou (OTE) v České republice. Aplikace poskytuje přehledné zobrazení výsledků různých trhů s elektřinou a plynem.

## Funkce

- Zobrazení výsledků denního trhu s elektřinou (DT)
- Zobrazení výsledků vnitrodenních auk<PERSON> (IDA1, IDA2, IDA3)
- Zobrazení výsledků kontinuálního vnitrodenního trhu (VDT-KON)
- Zobrazení výsledků trhu s plynem
- Automatické aktualizace dat v reálném čase
- Responzivní design pro všechna zařízení

## Technologie

- Backend: Python, Flask
- Frontend: HTML, CSS, JavaScript, Tailwind CSS
- Grafy: Chart.js
- Data: OTE API

## Instalace

### Požadavky

- Python 3.8+
- pip

### Postup instalace

1. Klonujte repozitář:
   ```
   git clone https://github.com/vas-projekt/ote-dashboard.git
   cd ote-dashboard
   ```

2. Vytvořte a aktivujte virtuální prostředí:
   ```
   python -m venv venv
   source venv/bin/activate  # Pro Windows: venv\Scripts\activate
   ```

3. Nainstalujte závislosti:
   ```
   pip install -r requirements.txt
   ```

4. Spusťte aplikaci:
   ```
   python python.py
   ```

5. Otevřete prohlížeč a přejděte na adresu:
   ```
   http://localhost:8082
   ```

## Struktura projektu

- `python.py` - Hlavní soubor aplikace s Flask routami
- `templates/` - HTML šablony pro jednotlivé stránky
- `static/` - Statické soubory (JavaScript, CSS, obrázky)
- `requirements.txt` - Seznam Python závislostí

## API Endpointy

- `/api/electricity-data` - Vrací data o elektřině
- `/api/gas-data` - Vrací data o plynu
- `/api/electricity-last-update` - Vrací čas poslední aktualizace dat o elektřině
- `/api/gas-last-update` - Vrací čas poslední aktualizace dat o plynu

## Dostupné stránky

- `/` - Hlavní stránka s přehledem
- `/dt` - Denní trh s elektřinou
- `/ida1` - Vnitrodenní aukce 1
- `/ida2` - Vnitrodenní aukce 2
- `/ida3` - Vnitrodenní aukce 3
- `/vdt-kon` - Kontinuální vnitrodenní trh
- `/gas` - Trh s plynem

## Produkční nasazení

Pro produkční nasazení doporučujeme použít Gunicorn:

```
gunicorn -w 4 -b 0.0.0.0:8082 python:app
```

## Licence

Tento projekt je licencován pod [MIT licencí](LICENSE).

## Autoři

- Váš tým