function getTodayDate() {
    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth() + 1).padStart(2, '0');
    const dd = String(now.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
}

async function fetchIdaSummary() {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    const prevDay = new Date(today);
    prevDay.setDate(today.getDate() - 1);
    const prevDayStr = prevDay.toISOString().split('T')[0];
    let url = `/api/ida-data?ida_session=IDA2&report_date=${todayStr}`;
    try {
        let response = await fetch(url);
        if (response.ok) {
            const data = await response.json();
            if (data && data.data && data.data.length > 0) {
                return data;
            }
        }
        url = `/api/ida-data?ida_session=IDA2&report_date=${prevDayStr}`;
        response = await fetch(url);
        if (response.ok) {
            const data = await response.json();
            if (data && data.data && data.data.length > 0) {
                data.fallback = true;
                return data;
            }
        }
        return null;
    } catch (e) {
        return null;
    }
}

async function updateSummaryCards() {
    const cacheKey = 'ida2ApiData';
    const cacheTimeKey = 'ida2ApiDataTime';
    const cacheDuration = 5 * 60 * 1000; // 5 minut
    const cachedData = localStorage.getItem(cacheKey);
    const cachedTime = localStorage.getItem(cacheTimeKey);
    let summary;
    if (cachedData && cachedTime && (Date.now() - cachedTime < cacheDuration)) {
        summary = JSON.parse(cachedData);
    } else {
        summary = await fetchIdaSummary();
        if (summary) {
            localStorage.setItem(cacheKey, JSON.stringify(summary));
            localStorage.setItem(cacheTimeKey, Date.now());
        }
    }
    if (summary && summary.data) {
        for (const item of summary.data) {
            if (item.index === 'BASE LOAD') {
                document.getElementById('base-load').textContent = item.price || '---';
            } else if (item.index === 'PEAK LOAD') {
                document.getElementById('peak-load').textContent = item.price || '---';
            } else if (item.index === 'OFFPEAK LOAD') {
                document.getElementById('offpeak-load').textContent = item.price || '---';
            }
        }
        document.getElementById('total-load').textContent = summary.total_amount !== null ? summary.total_amount.toFixed(1) : '---';
    } else {
        document.getElementById('base-load').textContent = '---';
        document.getElementById('peak-load').textContent = '---';
        document.getElementById('offpeak-load').textContent = '---';
        document.getElementById('total-load').textContent = '---';
    }
}

async function fetchData() {
    const date = getTodayDate();
    const url = `https://www.ote-cr.cz/cs/kratkodobe-trhy/elektrina/vnitrodenni-aukce-ida/@@chart-data?report_date=${date}&ida_session=IDA2&time_resolution=PT15M`;
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error('Chyba při načítání dat z URL: ' + response.statusText);
        }
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Chyba při načítání dat z URL:", error);
        return null;
    }
}

function formatHourLabels(dataPoints) {
    const labels = [];
    dataPoints.forEach((point, index) => {
        if (point % 4 === 0) {
            const hour = point / 4;
            labels.push(hour <= 24 ? hour.toString() : '');
        } else {
            labels.push('');
        }
    });
    return labels;
}

function createChart(canvasId, chartData) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    if (window.myChart) {
        window.myChart.destroy();
    }
    window.myChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.labels,
            datasets: [
                {
                    label: 'Množství (MWh)',
                    data: chartData.mnozstvi,
                    backgroundColor: 'rgba(75, 192, 192, 0.5)',
                    borderColor: 'rgba(75, 192, 192, 0.8)',
                    borderWidth: 2,
                    type: 'bar',
                    yAxisID: 'y-axis-2',
                    order: 1,
                },
                {
                    label: 'Cena (EUR/MWh)',
                    data: chartData.prumerCena,
                    borderColor: 'rgba(255, 99, 132, 1)',
                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                    borderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 7,
                    pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                    pointBorderColor: 'rgba(255, 99, 132, 1)',
                    type: 'line',
                    yAxisID: 'y-axis-1',
                    order: 2,
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Hodina',
                        color: '#333'
                    },
                    grid: {
                        display: true,
                        color: '#e0e0e0'
                    },
                    ticks: {
                        color: '#333',
                        font: {
                            size: 12,
                            family: 'Roboto',
                            weight: 'bold'
                        },
                        padding: 10,
                        maxRotation: 0,
                        minRotation: 0,
                        autoSkip: false,
                        callback: function(value, index) {
                            return chartData.labels[index];
                        }
                    }
                },
                'y-axis-1': {
                    type: 'linear',
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Cena (EUR/MWh)',
                        color: '#333'
                    },
                    grid: {
                        display: true,
                        color: '#e0e0e0'
                    },
                    ticks: {
                        color: '#333',
                        autoSkip: true,
                    },
                },
                'y-axis-2': {
                    type: 'linear',
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Množství (MWh)',
                        color: '#333'
                    },
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#333',
                        autoSkip: true,
                    },
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: '#333',
                        boxWidth: 30,
                        boxHeight: 30,
                        padding: 20,
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y.toFixed(2);
                            }
                            return label;
                        }
                    }
                }
            },
            elements: {
                line: {
                    tension: 0.4
                }
            }
        }
    });
}

async function updateDashboard() {
    const data = await fetchData();
    if (data) {
        const dataLines = data.data.dataLine;
        const graphTitle = data.graph.title;
        const dateMatch = graphTitle.match(/\d{2}\.\d{2}\.\d{4}/);
        if (dateMatch) {
            document.getElementById('market-date').textContent = dateMatch[0];
        } else {
            document.getElementById('market-date').textContent = 'N/A';
        }
        if (dataLines && dataLines.length > 0) {
            const quantityData = dataLines[0].point.map(p => ({ x: parseInt(p.x, 10), y: p.y }));
            const priceData = dataLines[1].point.map(p => ({ x: parseInt(p.x, 10), y: p.y }));
            const labels = formatHourLabels(quantityData.map(p => p.x));
            const chartData = {
                labels: labels,
                mnozstvi: quantityData.map(p => p.y),
                prumerCena: priceData.map(p => p.y)
            };
            createChart('oteChart', chartData);
        }
    } else {
        document.getElementById('market-date').textContent = 'Chyba při načítání dat';
    }
}

function updateDateTime() {
    const now = new Date();
    const options = {
        day: '2-digit', month: '2-digit', year: 'numeric',
        hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false
    };
    const dateTimeElement = document.getElementById('dateTime');
    if (dateTimeElement) {
        dateTimeElement.textContent = now.toLocaleString('cs-CZ', options);
    }
}

let timeUpdateInterval;
document.addEventListener('DOMContentLoaded', () => {
    updateDateTime();
    timeUpdateInterval = setInterval(updateDateTime, 1000);
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) { clearInterval(timeUpdateInterval); }
        else { updateDateTime(); timeUpdateInterval = setInterval(updateDateTime, 1000); }
    });
    updateDashboard();
    updateSummaryCards();
    setInterval(updateDashboard, 5 * 60 * 1000);
    setInterval(updateSummaryCards, 5 * 60 * 1000);
});
