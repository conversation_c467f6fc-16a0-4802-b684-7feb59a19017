/**
 * Optimalizovaný data loader pro rychlejší načítání API dat
 * Implementuje paralelní načítání, inteligentní cache a background refresh
 */

class OptimizedDataLoader {
    constructor() {
        this.cache = new Map();
        this.loadingPromises = new Map();
        this.refreshIntervals = new Map();
        this.defaultTimeout = 5000; // 5 sekund timeout
        this.cacheTimeout = 300000; // 5 minut cache
        this.backgroundRefreshThreshold = 0.8; // 80% cache času
        
        // Inicializace
        this.init();
    }
    
    init() {
        // Spusť background refresh pro kritická data
        this.startBackgroundRefresh();
        
        // Event listener pro visibility change (když uživatel přepne tab)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.refreshCriticalData();
            }
        });
    }
    
    /**
     * Paralelně načte všechna data současně
     */
    async loadAllData() {
        const cacheKey = 'all_data';
        
        // Zkontroluj cache
        const cachedData = this.getFromCache(cacheKey);
        if (cachedData) {
            // Spusť background refresh pokud jsou data blízko expiraci
            if (this.shouldBackgroundRefresh(cacheKey)) {
                this.backgroundRefreshAllData();
            }
            return cachedData;
        }
        
        // Pokud už probíhá načítání, vrať existující promise
        if (this.loadingPromises.has(cacheKey)) {
            return this.loadingPromises.get(cacheKey);
        }
        
        // Spusť paralelní načítání
        const loadingPromise = this.fetchAllDataParallel();
        this.loadingPromises.set(cacheKey, loadingPromise);
        
        try {
            const data = await loadingPromise;
            this.setCache(cacheKey, data);
            return data;
        } finally {
            this.loadingPromises.delete(cacheKey);
        }
    }
    
    /**
     * Paralelní fetch všech dat
     */
    async fetchAllDataParallel() {
        const requests = [
            this.fetchWithTimeout('/api/electricity-data'),
            this.fetchWithTimeout('/api/gas-data'),
            this.fetchWithTimeout('/api/ida-data?ida_session=IDA1&report_date=' + this.getTodayString())
        ];
        
        try {
            const results = await Promise.allSettled(requests);
            
            return {
                electricity: this.extractData(results[0]),
                gas: this.extractData(results[1]),
                ida1: this.extractData(results[2]),
                timestamp: Date.now()
            };
        } catch (error) {
            console.error('Error in parallel data fetch:', error);
            throw error;
        }
    }
    
    /**
     * Načte data s timeout
     */
    async fetchWithTimeout(url, timeout = this.defaultTimeout) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            const response = await fetch(url, {
                signal: controller.signal,
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } finally {
            clearTimeout(timeoutId);
        }
    }
    
    /**
     * Extrahuje data z Promise.allSettled výsledku
     */
    extractData(result) {
        if (result.status === 'fulfilled') {
            return result.value;
        } else {
            console.error('Failed to fetch data:', result.reason);
            return { data: [], error: result.reason.message };
        }
    }
    
    /**
     * Optimalizované načtení specifických IDA dat
     */
    async loadIdaData(session, date = null) {
        const reportDate = date || this.getTodayString();
        const cacheKey = `ida_${session}_${reportDate}`;

        // Zkontroluj cache
        const cachedData = this.getFromCache(cacheKey);
        if (cachedData) {
            console.log(`IDA ${session} data loaded from cache`);
            return cachedData;
        }

        // Pokud už probíhá načítání, vrať existující promise
        if (this.loadingPromises.has(cacheKey)) {
            return this.loadingPromises.get(cacheKey);
        }

        const startTime = performance.now();

        try {
            // Optimalizované paralelní načítání s kratšími timeouty
            const loadingPromise = Promise.all([
                this.fetchWithTimeout(`/api/ida-data?ida_session=${session}&report_date=${reportDate}`, 3000),
                this.fetchWithTimeout(`/api/ida-chart-data?ida_session=${session}&report_date=${reportDate}`, 3000)
            ]);

            this.loadingPromises.set(cacheKey, loadingPromise);

            const [cardData, chartData] = await loadingPromise;
            const loadTime = Math.round(performance.now() - startTime);

            const combinedData = {
                card: cardData,
                chart: chartData,
                performance: {
                    loadTime: loadTime,
                    session: session,
                    cached: false
                },
                timestamp: Date.now()
            };

            this.setCache(cacheKey, combinedData);
            console.log(`IDA ${session} data loaded in ${loadTime}ms`);

            return combinedData;
        } catch (error) {
            console.error(`Error loading IDA data for ${session}:`, error);
            throw error;
        } finally {
            this.loadingPromises.delete(cacheKey);
        }
    }

    /**
     * Rychlé načtení jen card dat (bez chart)
     */
    async loadIdaCardDataOnly(session, date = null) {
        const reportDate = date || this.getTodayString();
        const cacheKey = `ida_card_${session}_${reportDate}`;

        // Zkontroluj cache
        const cachedData = this.getFromCache(cacheKey);
        if (cachedData) {
            return cachedData;
        }

        const startTime = performance.now();

        try {
            const cardData = await this.fetchWithTimeout(
                `/api/ida-data?ida_session=${session}&report_date=${reportDate}`,
                2000  // Kratší timeout pro jen card data
            );

            const loadTime = Math.round(performance.now() - startTime);

            const result = {
                ...cardData,
                performance: {
                    loadTime: loadTime,
                    session: session,
                    cached: false,
                    cardOnly: true
                }
            };

            this.setCache(cacheKey, result);
            console.log(`IDA ${session} card data loaded in ${loadTime}ms`);

            return result;
        } catch (error) {
            console.error(`Error loading IDA card data for ${session}:`, error);
            throw error;
        }
    }

    /**
     * Paralelní načtení všech IDA sessions
     */
    async loadAllIdaData(date = null) {
        const reportDate = date || this.getTodayString();
        const sessions = ['IDA1', 'IDA2', 'IDA3'];

        const startTime = performance.now();

        try {
            // Načti všechny IDA sessions paralelně
            const promises = sessions.map(session =>
                this.loadIdaCardDataOnly(session, reportDate).catch(error => {
                    console.error(`Failed to load ${session}:`, error);
                    return { error: error.message, session: session };
                })
            );

            const results = await Promise.all(promises);
            const totalLoadTime = Math.round(performance.now() - startTime);

            const combinedResult = {
                ida1: results[0],
                ida2: results[1],
                ida3: results[2],
                performance: {
                    totalLoadTime: totalLoadTime,
                    parallelSessions: sessions.length,
                    timestamp: Date.now()
                }
            };

            console.log(`All IDA data loaded in ${totalLoadTime}ms`);
            return combinedResult;

        } catch (error) {
            console.error('Error loading all IDA data:', error);
            throw error;
        }
    }
    
    /**
     * Background refresh všech dat
     */
    backgroundRefreshAllData() {
        // Spusť v background bez čekání
        this.fetchAllDataParallel()
            .then(data => {
                this.setCache('all_data', data);
                console.log('Background refresh completed');
                
                // Trigger custom event pro komponenty
                window.dispatchEvent(new CustomEvent('dataRefreshed', { 
                    detail: data 
                }));
            })
            .catch(error => {
                console.error('Background refresh failed:', error);
            });
    }
    
    /**
     * Spustí pravidelný background refresh
     */
    startBackgroundRefresh() {
        // Refresh každých 5 minut
        const refreshInterval = setInterval(() => {
            this.backgroundRefreshAllData();
        }, 300000); // 5 minut
        
        this.refreshIntervals.set('main', refreshInterval);
    }
    
    /**
     * Refresh kritických dat při focus
     */
    refreshCriticalData() {
        const criticalKeys = ['all_data'];
        
        criticalKeys.forEach(key => {
            if (this.shouldBackgroundRefresh(key)) {
                this.backgroundRefreshAllData();
            }
        });
    }
    
    /**
     * Cache management
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (!cached) return null;
        
        const now = Date.now();
        if (now - cached.timestamp > this.cacheTimeout) {
            this.cache.delete(key);
            return null;
        }
        
        return cached.data;
    }
    
    setCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }
    
    shouldBackgroundRefresh(key) {
        const cached = this.cache.get(key);
        if (!cached) return false;
        
        const now = Date.now();
        const age = now - cached.timestamp;
        const threshold = this.cacheTimeout * this.backgroundRefreshThreshold;
        
        return age > threshold;
    }
    
    /**
     * Utility funkce
     */
    getTodayString() {
        return new Date().toISOString().split('T')[0];
    }
    
    /**
     * Vyčistí všechny cache a intervaly
     */
    cleanup() {
        this.cache.clear();
        this.loadingPromises.clear();
        
        this.refreshIntervals.forEach(interval => {
            clearInterval(interval);
        });
        this.refreshIntervals.clear();
    }
}

// Globální instance
window.optimizedDataLoader = new OptimizedDataLoader();

// Export pro moduly
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OptimizedDataLoader;
}

// Auto cleanup při unload
window.addEventListener('beforeunload', () => {
    if (window.optimizedDataLoader) {
        window.optimizedDataLoader.cleanup();
    }
});

console.log('Optimized Data Loader initialized');
