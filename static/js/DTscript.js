function updateDateTime() {
    const now = new Date();
    document.getElementById('dateTime').textContent = now.toLocaleString('cs-CZ', {
        day: '2-digit', month: '2-digit', year: 'numeric',
        hour: '2-digit', minute: '2-digit', second: '2-digit'
    });
}

setInterval(updateDateTime, 1000);

async function fetchData() {
    const today = new Date().toISOString().split('T')[0];
    const url = `https://www.ote-cr.cz/cs/kratkodobe-trhy/elektrina/denni-trh/@@chart-data?report_date=`;

    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error('Chyba při načítání dat z URL: ' + response.statusText);
        }
        const data = await response.json();
        console.log("Data úspěšně načtena z URL:", data);
        return data;
    } catch (error) {
        console.error("Chyba při načítání dat z URL:", error);
        return null;
    }
}

function updateMetric(id, value) {
    document.getElementById(id).textContent = value !== undefined ? value : '---';
}

function createChart(canvasId, chartData) {
    const ctx = document.getElementById(canvasId).getContext('2d');

    if (window.myChart) {
        window.myChart.destroy();
    }

    window.myChart = new Chart(ctx, {
        data: {
            labels: chartData.labels,
            datasets: [
                {
                    label: 'Cena (EUR/MWh)',
                    data: chartData.cena,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderWidth: 2,
                    pointRadius: 4,
                    pointBackgroundColor: 'white',
                    pointBorderColor: 'rgb(255, 99, 132)',
                    type: 'line',
                    yAxisID: 'y-axis-1'
                },
                {
                    label: 'Množství (MWh)',
                    data: chartData.mnozstvi,
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    type: 'bar',
                    yAxisID: 'y-axis-2'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Hodina',
                        color: '#333'
                    },
                    grid: {
                        display: true,
                        color: '#e0e0e0'
                    },
                    ticks: {
                        color: '#333'
                    }
                },
                'y-axis-1': {
                    type: 'linear',
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Cena (EUR/MWh)',
                        color: '#333'
                    },
                    beginAtZero: true,
                    grid: {
                        display: true,
                        color: '#e0e0e0'
                    },
                    ticks: {
                        color: '#333'
                    }
                },
                'y-axis-2': {
                    type: 'linear',
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Množství (MWh)',
                        color: '#333'
                    },
                    beginAtZero: true,
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#333'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: '#333',
                        boxWidth: 25,
                        boxHeight: 25,
                        padding: 25,
                        usePointStyle: false,
                        generateLabels: function (chart) {
                            const labels = Chart.defaults.plugins.legend.labels.generateLabels(chart);
                            labels.forEach((label, index) => {
                                if (index === 0) {
                                    label.fillStyle = 'rgba(255, 99, 132, 0.5)';
                                    label.strokeStyle = 'rgb(255, 99, 132)';
                                    label.lineWidth = 2;
                                } else if (index === 1) {
                                    label.fillStyle = 'rgba(54, 162, 235, 0.8)';
                                    label.strokeStyle = 'rgba(54, 162, 235, 1)';
                                    label.lineWidth = 2;
                                }
                            });
                            return labels;
                        }
                    },
                    onClick: function (e, legendItem, legend) {
                        const index = legendItem.datasetIndex;
                        const ci = legend.chart;
                        const meta = ci.getDatasetMeta(index);

                        meta.hidden = meta.hidden === null ? !ci.data.datasets[index].hidden : null;
                        ci.update();
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y.toFixed(2);
                            }
                            return label;
                        }
                    }
                }
            }
        }
    });
}

async function updateDashboard() {
    const data = await fetchData();

    if (data) {
        const { dataLine } = data.data;
        const graphTitle = data.graph.title;
        const dateMatch = graphTitle.match(/\d{2}\.\d{2}\.\d{4}/);

        if (dateMatch) {
            document.getElementById('market-date').textContent = dateMatch[0];
        } else {
            document.getElementById('market-date').textContent = 'N/A';
        }

        if (dataLine && dataLine.length > 0) {
            const chartData = {
                labels: dataLine[0].point.map(p => p.x),
                mnozstvi: dataLine[0].point.map(p => p.y),
                cena: dataLine[1].point.map(p => p.y)
            };

            createChart('oteChart', chartData);

            // Filtruj pouze hodnoty za den (hodiny 1-24 nebo 0-23)
            const dailyPoints = dataLine[0].point.filter(p => {
                // Pokud x je číslo hodiny, např. 1-24 nebo 0-23
                const hour = parseInt(p.x, 10);
                return !isNaN(hour) && hour >= 1 && hour <= 24;
            });
            const totalLoad = dailyPoints.reduce((sum, p) => sum + (typeof p.y === 'number' ? p.y : 0), 0);
            document.getElementById('total-load').textContent = totalLoad ? totalLoad.toLocaleString('cs-CZ', {maximumFractionDigits: 2}) : '---';

            updateMetric('base-value', '---');
            updateMetric('peak-value', '---');
            updateMetric('offpeak-value', '---');
        }
    } else {
        console.error("Nepodařilo se načíst data z API.");
        document.getElementById('market-date').textContent = 'Chyba při načítání dat';
    }
}

window.onload = function() {
    updateDashboard();
    updateDateTime();
    setInterval(updateDashboard, 5 * 60 * 1000);
};

let chart;

async function fetchAndDisplayDT() {
    const cacheKey = 'dtApiData';
    const cacheTimeKey = 'dtApiDataTime';
    const cacheDuration = 5 * 60 * 1000; // 5 minut
    const cachedData = localStorage.getItem(cacheKey);
    const cachedTime = localStorage.getItem(cacheTimeKey);
    let data;
    if (cachedData && cachedTime && (Date.now() - cachedTime < cacheDuration)) {
        data = JSON.parse(cachedData);
    } else {
        try {
            const resp = await fetch('/api/electricity-data');
            const json = await resp.json();
            data = json.data;
            localStorage.setItem(cacheKey, JSON.stringify(data));
            localStorage.setItem(cacheTimeKey, Date.now());
        } catch (e) {
            data = null;
        }
    }
    try {
        const base = data?.find(item => item.commodity && item.commodity.toUpperCase().includes('BASE LOAD'));
        const peak = data?.find(item => item.commodity && item.commodity.toUpperCase().includes('PEAK LOAD'));
        const offpeak = data?.find(item => item.commodity && item.commodity.toUpperCase().includes('OFFPEAK LOAD'));
        document.getElementById('base-load').textContent = base?.price || '-';
        document.getElementById('peak-load').textContent = peak?.price || '-';
        document.getElementById('offpeak-load').textContent = offpeak?.price || '-';
    } catch (e) {
        document.getElementById('base-load').textContent = '-';
        document.getElementById('peak-load').textContent = '-';
        document.getElementById('offpeak-load').textContent = '-';
    }
}

window.addEventListener('DOMContentLoaded', () => {
    fetchAndDisplayDT();
    setInterval(fetchAndDisplayDT, 5 * 60 * 1000);
});
