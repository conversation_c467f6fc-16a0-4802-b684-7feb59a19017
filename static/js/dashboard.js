class GlobalDashboardManager {
  constructor() {
    this.progressBar = document.getElementById('autoSwitchProgressBar');
    this.progressBarWrapper = document.getElementById('autoSwitchProgressBarWrapper');
    this.autoSwitchDuration = 999999;
    this.autoSwitchTimer = null;
    this.autoSwitchElapsed = 0;
    this.isTransitioning = false;
    this.rotationSequence = [
      { url: '/' },
      { url: '/dt' },
      { url: '/vdt-kon' },
      { url: '/ida1' },
      { url: '/ida2' },
      { url: '/ida3' },
      { url: '/vdt-p' }
    ];
    this.startAutoSwitchCountdown();
    this.initializeDateTime();
  }

  startAutoSwitchCountdown() {
    if (!this.progressBar || !this.progressBarWrapper) return;
    this.progressBarWrapper.style.display = '';
    this.progressBar.style.width = '100%';
    this.autoSwitchElapsed = 0;
    if (this.autoSwitchTimer) clearInterval(this.autoSwitchTimer);
    this.autoSwitchTimer = setInterval(() => {
      this.autoSwitchElapsed++;
      const percent = Math.max(0, 100 - (this.autoSwitchElapsed / this.autoSwitchDuration) * 100);
      this.progressBar.style.width = percent + '%';
      if (this.autoSwitchElapsed >= this.autoSwitchDuration) {
        this.handleRotation();
        this.resetProgressBar();
      }
    }, 1000);
  }

  resetProgressBar() {
    this.autoSwitchElapsed = 0;
    this.progressBar.style.width = '100%';
  }

  getCurrentIndex() {
    let path = window.location.pathname.replace(/\.html$/, '');
    if (path === '' || path === '/') path = '/';
    for (let i = 0; i < this.rotationSequence.length; i++) {
      if (this.rotationSequence[i].url === path) {
        return i;
      }
    }
    return 0;
  }

  handleRotation() {
    const currentIndex = this.getCurrentIndex();
    const nextIndex = (currentIndex + 1) % this.rotationSequence.length;
    const next = this.rotationSequence[nextIndex];
    window.location.href = next.url;
  }

  initializeDateTime() {
    const updateDateTime = () => {
      const now = new Date();
      const options = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      };
      const dateTimeElement = document.getElementById('dateTime');
      if (dateTimeElement) {
        dateTimeElement.textContent = now.toLocaleString('cs-CZ', options);
      }
    };
    updateDateTime();
    setInterval(updateDateTime, 1000);
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) updateDateTime();
    });
  }
}

document.addEventListener('DOMContentLoaded', () => {
  new GlobalDashboardManager();
});

setTimeout(function() {
  window.location.reload();
}, 30 * 60 * 1000);

document.addEventListener('DOMContentLoaded', function() {
  const animateElements = document.querySelectorAll('.tv-card, .timeline-item');

  animateElements.forEach((element, index) => {
    setTimeout(() => {
      element.style.opacity = '1';
      element.style.transform = 'translateY(0)';
    }, 100 * index);
  });
});
