:root {
  --primary-color: #4F46E5;
  --secondary-color: #10B981;
  --background-color: #F3F4F6;
  --text-color: #1F2937;
  --card-background: #FFFFFF;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --primary-gradient: linear-gradient(135deg, var(--primary-color), #0288d1);
  --gas-gradient: linear-gradient(135deg, #FFB83F, #FFD391);
  --success-color: #22c55e;
  --danger-color: #ef4444;
  --text-light: rgba(255, 255, 255, 0.9);
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
}

body {
  font-family: 'Inter', sans-serif;
  background: #f8fafc;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  color: #1a1f2b;
  position: relative;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: var(--space-md);
}

.header {
  background-color: var(--card-background);
  box-shadow: var(--card-shadow);
  padding: 1rem;
  margin-bottom: 2rem;
}

.card {
  position: relative;
  background-color: var(--card-background);
  border-radius: 0.5rem;
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  overflow: hidden;
  transition: transform 0.2s ease-in-out;
}

.card.gas-card {
  background: var(--gas-gradient);
  color: #1a202c;
}

.card:hover {
  transform: translateY(-5px);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.card-change {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.card-change.positive {
  color: var(--secondary-color);
}

.card-change.negative {
  color: #EF4444;
}

.grid {
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.dashboard-section .cards {
  grid-template-columns: repeat(4, 1fr);
}

.dashboard-section .card {
  align-items: center;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  min-height: 180px;
  overflow: hidden;
  padding: var(--space-xl);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
}

.card-gradient {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(37, 99, 235, 0.9), rgba(30, 58, 138, 0.9));
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease-in-out;
  max-width: 350px;
  flex: 0 0 auto;
}

.card-gradient:hover {
  transform: translateY(-4px);
}

.card-content {
  position: relative;
  z-index: 10;
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.card-header {
  padding-bottom: 0.75rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.metric-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
}

.metric-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

.card-footer {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
}

.percentage-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: 600;
  font-size: 0.875rem;
  backdrop-filter: blur(4px);
}

.percentage-badge.positive {
  background-color: rgba(34, 197, 94, 0.75);
}

.percentage-badge.negative {
  background-color: rgba(239, 68, 68, 0.75);
}

@media (max-width: 640px) {
  .card-gradient {
    min-width: 280px;
  }
  
  .metric-value {
    font-size: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .tv-container {
    padding: 0 2rem;
  }
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.hidden {
  display: none;
}

.glass-effect {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid #e2e8f0;
}

.section-header .icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-md);
}

.toggle-button {
  background-color: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.toggle-button:hover {
  background-color: #4338CA;
}

@media (min-width: 640px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.tv-container {
  max-width: 1920px;
  margin: 0 auto;
  padding: 3rem;
}

.scroll-section {
  padding: 1rem;
  margin: -1rem;
  overflow-x: auto;
  scrollbar-width: thin;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.flex-nowrap {
  display: flex;
  gap: 1.25rem;
  padding: 0.75rem 0.5rem;
}

.gas-card-dark {
  background: #e09b19 !important;
  color: #fff !important;
}

.settlement-value {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: #222 !important;
  line-height: 1.1 !important;
}

.tv-card {
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 15px 35px -5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  animation: fade-in 0.5s ease-out;
  border: 2px solid rgba(0, 0, 0, 0.05);
}
.agenda-item {
  border: 1px solid rgba(59, 130, 246, 0.1);
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
}
.electricity-icon {
  color: #004C97;
}
.gas-icon {
  color: #D97706;
}
.timeline-item {
  padding-left: 25px;
  position: relative;
  margin-bottom: 1rem;
}
.timeline-dot {
  position: absolute;
  left: -12px;
  top: 4px;
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  border: 3px solid #004C97;
  z-index: 1;
}
.timeline-dot-last {
  position: absolute;
  left: -12px;
  top: 4px;
  width: 16px;
  height: 16px;
  background-color: #004C97;
  border-radius: 50%;
  border: 3px solid white;
  z-index: 1;
}
.tv-hero {
  position: relative;
  overflow: hidden;
}
.bg-pattern {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
  pointer-events: none;
  z-index: -1;
  opacity: 0.7;
}
.tv-title {
  font-size: 2.5rem;
  line-height: 1.1;
  font-weight: 700;
}
.tv-subtitle {
  font-size: 1.5rem;
  line-height: 1.3;
}
.tv-text-lg {
  font-size: 1.25rem;
  line-height: 1.5;
}
.tv-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 400px), 1fr));
  gap: 1rem;
}
.scrollbar-thin::-webkit-scrollbar {
  height: 6px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}
.market-switch {
  position: fixed;
  top: 1.5rem;
  right: 1.5rem;
  z-index: 1000;
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  padding: 0.25rem;
}
.market-switch button {
  padding: 0.625rem 1.25rem;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.market-switch button.active-electricity {
  background: #004C97;
  color: #ffffff;
  box-shadow: 0 2px 5px rgba(0, 114, 188, 0.2);
}
.market-switch button.active-gas {
  background: #D97706;
  color: #ffffff;
  box-shadow: 0 2px 5px rgba(255, 167, 38, 0.2);
}
.market-switch button:not(.active-electricity):not(.active-gas) {
  color: #ffffff;
}
.unit-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
  letter-spacing: 0.025em;
}
/* --- konec custom stylů z index.html --- */

/* --- Přidáno z DT.html --- */
.glass-effect {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.1);
}
.card-gradient {
  background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.price-card-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-bottom: 2.5rem;
}
.price-card-group {
  grid-column: span 3;
  background: #eaf3fb;
  border: 1.5px solid #c3e0fa;
  border-radius: 16px;
  padding: 18px 18px;
  display: flex;
  gap: 2rem;
  align-items: stretch;
  height: 100%;
}
.price-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 114, 188, 0.07);
  text-align: center;
  min-width: 200px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.price-card-header {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}
.price-card-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: #0072bc;
}
.price-card-currency {
  font-size: 1.1rem;
  color: #6b7280;
}
@media (max-width: 900px) {
  .price-card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
  }
  .price-card-group {
    grid-column: span 2;
    gap: 1.2rem;
  }
}
